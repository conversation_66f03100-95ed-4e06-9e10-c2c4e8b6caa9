<?php

namespace App\Livewire\WebsiteMonitoring;

use Livewire\Component;
use App\Models\Website;
use App\Services\WebsiteMonitoringService;
use Illuminate\Support\Facades\Auth;

class WebsiteList extends Component
{
    public $search = '';
    public $statusFilter = 'all';
    public $lastRefresh;

    // Pagination properties
    public $perPage = 25;
    public $currentPage = 1;
    public $totalItems = 0;

    protected $queryString = [
        'search' => ['except' => ''],
        'statusFilter' => ['except' => 'all'],
        'currentPage' => ['except' => 1],
        'perPage' => ['except' => 25]
    ];

    public function mount()
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $this->lastRefresh = now()->format('H:i:s');
    }

    public function refreshData()
    {
        $this->lastRefresh = now()->format('H:i:s');
        // The render method will automatically fetch fresh data
    }

    public function updatedSearch()
    {
        $this->currentPage = 1;
    }

    public function updatedStatusFilter()
    {
        $this->currentPage = 1;
    }

    public function updatedPerPage()
    {
        $this->currentPage = 1;
    }

    // Pagination methods
    public function nextPage()
    {
        $maxPage = ceil($this->totalItems / $this->perPage);
        if ($this->currentPage < $maxPage) {
            $this->currentPage++;
        }
    }

    public function previousPage()
    {
        if ($this->currentPage > 1) {
            $this->currentPage--;
        }
    }

    public function goToPage($page)
    {
        $maxPage = ceil($this->totalItems / $this->perPage);
        if ($page >= 1 && $page <= $maxPage) {
            $this->currentPage = $page;
        }
    }

    public function checkWebsite($websiteId)
    {
        $website = Website::with('urls')->findOrFail($websiteId);
        $monitoringService = new WebsiteMonitoringService();

        foreach ($website->urls as $url) {
            if ($url->monitor_status || $url->monitor_domain || $url->monitor_ssl) {
                $monitoringService->monitorWebsiteUrl($url);
            }
        }

        session()->flash('message', 'Website monitoring check completed.');
        $this->dispatch('refreshWebsites');
    }

    public function deleteWebsite($websiteId)
    {
        $website = Website::findOrFail($websiteId);
        $website->delete();

        session()->flash('message', 'Website deleted successfully.');
        $this->dispatch('refreshWebsites');
    }

    public function toggleWebsiteStatus($websiteId)
    {
        $website = Website::findOrFail($websiteId);
        $website->update(['is_active' => !$website->is_active]);

        $message = $website->is_active ? 'Website activated.' : 'Website deactivated.';
        session()->flash('message', $message);
        $this->dispatch('refreshWebsites');
    }

    public function render()
    {
        $query = Website::with(['urls' => function ($query) {
            $query->latest('last_checked_at');
        }]);

        if ($this->search) {
            $searchTerm = strtolower($this->search);
            $query->where(function($q) use ($searchTerm) {
                // Search in website name
                $q->whereRaw('LOWER(name) LIKE ?', ['%' . $searchTerm . '%'])
                  // Search in website description
                  ->orWhereRaw('LOWER(description) LIKE ?', ['%' . $searchTerm . '%'])
                  // Search in URLs
                  ->orWhereHas('urls', function($urlQuery) use ($searchTerm) {
                      $urlQuery->whereRaw('LOWER(url) LIKE ?', ['%' . $searchTerm . '%']);
                  });
            });
        }

        if ($this->statusFilter !== 'all') {
            $query->where('overall_status', $this->statusFilter);
        }

        // Get total count for pagination
        $this->totalItems = $query->count();

        // Apply pagination manually
        $websites = $query->latest()
            ->skip(($this->currentPage - 1) * $this->perPage)
            ->take($this->perPage)
            ->get();

        return view('livewire.website-monitoring.website-list', [
            'websites' => $websites
        ])->layout('layouts.main');
    }
}
