@if($totalItems > 0)
<div class="flex flex-col sm:flex-row justify-between items-center mt-6 px-6 py-4 bg-white border border-gray-200 rounded-lg shadow-sm">
    <div class="text-sm text-gray-700 mb-2 sm:mb-0">
        Showing 
        <span class="font-medium">{{ ($currentPage - 1) * $perPage + 1 }}</span>
        to 
        <span class="font-medium">{{ min($currentPage * $perPage, $totalItems) }}</span>
        of 
        <span class="font-medium">{{ $totalItems }}</span>
        websites
    </div>
    
    <div class="flex space-x-1">
        <!-- Previous Button -->
        <button 
            wire:click="previousPage" 
            class="px-3 py-2 rounded-md border {{ $currentPage <= 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200' : 'bg-white text-gray-700 hover:bg-gray-50 border-gray-300 hover:border-red-300' }} transition-colors duration-200"
            {{ $currentPage <= 1 ? 'disabled' : '' }}
        >
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
        </button>
        
        @php
            $maxPage = ceil($totalItems / $perPage);
            $pagesToShow = 5;
            $halfPagesToShow = floor($pagesToShow / 2);
            
            if ($maxPage <= $pagesToShow) {
                $startPage = 1;
                $endPage = $maxPage;
            } elseif ($currentPage <= $halfPagesToShow) {
                $startPage = 1;
                $endPage = $pagesToShow;
            } elseif ($currentPage >= $maxPage - $halfPagesToShow) {
                $startPage = $maxPage - $pagesToShow + 1;
                $endPage = $maxPage;
            } else {
                $startPage = $currentPage - $halfPagesToShow;
                $endPage = $currentPage + $halfPagesToShow;
            }
            
            $startPage = max(1, $startPage);
            $endPage = min($maxPage, $endPage);
        @endphp
        
        <!-- First page if not in range -->
        @if($startPage > 1)
            <button wire:click="goToPage(1)" class="px-3 py-2 rounded-md border bg-white text-gray-700 hover:bg-gray-50 border-gray-300 hover:border-red-300 transition-colors duration-200">
                1
            </button>
            
            @if($startPage > 2)
                <span class="px-3 py-2 text-gray-500">...</span>
            @endif
        @endif
        
        <!-- Page numbers -->
        @for($i = $startPage; $i <= $endPage; $i++)
            <button 
                wire:click="goToPage({{ $i }})" 
                class="px-3 py-2 rounded-md border {{ $currentPage == $i ? 'bg-red-600 text-white border-red-600' : 'bg-white text-gray-700 hover:bg-gray-50 border-gray-300 hover:border-red-300' }} transition-colors duration-200"
            >
                {{ $i }}
            </button>
        @endfor
        
        <!-- Last page if not in range -->
        @if($endPage < $maxPage)
            @if($endPage < $maxPage - 1)
                <span class="px-3 py-2 text-gray-500">...</span>
            @endif
            
            <button wire:click="goToPage({{ $maxPage }})" class="px-3 py-2 rounded-md border bg-white text-gray-700 hover:bg-gray-50 border-gray-300 hover:border-red-300 transition-colors duration-200">
                {{ $maxPage }}
            </button>
        @endif
        
        <!-- Next Button -->
        <button 
            wire:click="nextPage" 
            class="px-3 py-2 rounded-md border {{ $currentPage >= $maxPage ? 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200' : 'bg-white text-gray-700 hover:bg-gray-50 border-gray-300 hover:border-red-300' }} transition-colors duration-200"
            {{ $currentPage >= $maxPage ? 'disabled' : '' }}
        >
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
        </button>
    </div>
    
    <!-- Items per page selector -->
    <div class="flex items-center space-x-2 mt-2 sm:mt-0">
        <span class="text-sm text-gray-700">Items per page:</span>
        <select wire:model.live="perPage" class="border-gray-300 rounded-md text-sm focus:ring-red-500 focus:border-red-500 bg-white">
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
            <option value="100">100</option>
        </select>
    </div>
</div>
@endif
